#!/bin/bash

# SkyWars Command Integration Script
# Provides basic command functionality

COMMAND="$1"
PLAYER="$2"
ARG1="$3"

case "$COMMAND" in
    "join")
        echo "Attempting to join SkyWars game for player: $PLAYER"
        docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $PLAYER {\"text\":\"Joining SkyWars game...\",\"color\":\"green\"}"
        ;;
    "leave")
        echo "Player $PLAYER leaving SkyWars"
        docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $PLAYER {\"text\":\"Left SkyWars queue\",\"color\":\"yellow\"}"
        ;;
    "stats")
        echo "Showing stats for player: $PLAYER"
        docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $PLAYER {\"text\":\"SkyWars Stats: Wins: 0, Kills: 0\",\"color\":\"aqua\"}"
        ;;
    *)
        echo "Unknown command: $COMMAND"
        ;;
esac
