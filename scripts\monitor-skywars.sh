#!/bin/bash

# SkyWars System Monitor
# Checks system health and reports issues

echo "🔍 SkyWars System Health Check"
echo "=============================="

# Check server status
if docker ps | grep -q minecraft-server-docker-mc-1; then
    echo "✅ Server: Running"
else
    echo "❌ Server: Not running"
fi

# Check database status
if docker ps | grep -q mincraft-server-docker-db-1; then
    echo "✅ Database: Running"
else
    echo "❌ Database: Not running"
fi

# Check plugin files
if [[ -f "plugins/SkyWarsReloaded.jar" ]] || [[ -f "plugins/SkyWars.jar" ]]; then
    echo "✅ SkyWars Plugin: Installed"
else
    echo "⚠️  SkyWars Plugin: Not found"
fi

# Check configuration files
if [[ -f "plugins/SkyWars/config.yml" ]]; then
    echo "✅ Configuration: Present"
else
    echo "❌ Configuration: Missing"
fi

# Check world
if [[ -d "modern_skywar" ]]; then
    echo "✅ World: Available"
else
    echo "❌ World: Missing"
fi

echo ""
echo "📊 System Status Summary:"
echo "========================"

# Get server TPS if possible
TPS=$(docker exec minecraft-server-docker-mc-1 rcon-cli "tps" 2>/dev/null | grep -o '[0-9]*\.[0-9]*' | head -1 || echo "Unknown")
echo "Server TPS: $TPS"

# Get memory usage
MEMORY=$(docker stats minecraft-server-docker-mc-1 --no-stream --format "{{.MemUsage}}" 2>/dev/null || echo "Unknown")
echo "Memory Usage: $MEMORY"

echo ""
echo "🎮 Ready for SkyWars: $(if [[ -f "plugins/SkyWars/config.yml" && -d "modern_skywar" ]]; then echo "YES"; else echo "NO"; fi)"
