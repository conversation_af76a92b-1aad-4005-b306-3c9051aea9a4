#!/bin/bash

# 📊 SkyWars Performance Monitoring System
# Comprehensive system health checks and performance optimization
# Version: 2.1 - Enhanced Performance Monitoring

set -e

echo "📊 Setting up SkyWars Performance Monitoring..."
echo "==============================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_perf() { echo -e "${PURPLE}📊 $1${NC}"; }

# Performance thresholds
TPS_WARNING=18.0
TPS_CRITICAL=15.0
MEMORY_WARNING=80
MEMORY_CRITICAL=90
DISK_WARNING=80
DISK_CRITICAL=90
RESPONSE_TIME_WARNING=1000
RESPONSE_TIME_CRITICAL=3000

# Create system performance monitor
create_system_monitor() {
    log_perf "Creating system performance monitor..."

    cat > scripts/system-performance-monitor.sh << 'EOF'
#!/bin/bash
# System Performance Monitor
# Monitors server, database, and system resources

# Performance thresholds
TPS_WARNING=18.0
TPS_CRITICAL=15.0
MEMORY_WARNING=80
MEMORY_CRITICAL=90
DISK_WARNING=80
DISK_CRITICAL=90

# Function to get server TPS
get_server_tps() {
    local tps=$(docker exec minecraft-server-docker-mc-1 rcon-cli "tps" 2>/dev/null | grep -o '[0-9]*\.[0-9]*' | head -1 || echo "0")
    echo "$tps"
}

# Function to get memory usage
get_memory_usage() {
    local memory_info=$(docker stats minecraft-server-docker-mc-1 --no-stream --format "{{.MemUsage}}" 2>/dev/null || echo "0B / 0B")
    local used=$(echo "$memory_info" | cut -d'/' -f1 | sed 's/[^0-9.]//g')
    local total=$(echo "$memory_info" | cut -d'/' -f2 | sed 's/[^0-9.]//g')

    if [[ "$total" != "0" && -n "$total" ]]; then
        local percent=$(echo "scale=1; ($used / $total) * 100" | bc -l 2>/dev/null || echo "0")
        echo "$percent"
    else
        echo "0"
    fi
}

# Function to get disk usage
get_disk_usage() {
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    echo "$disk_usage"
}

# Function to get CPU usage
get_cpu_usage() {
    local cpu_usage=$(docker stats minecraft-server-docker-mc-1 --no-stream --format "{{.CPUPerc}}" 2>/dev/null | sed 's/%//' || echo "0")
    echo "$cpu_usage"
}

# Function to check server responsiveness
check_server_responsiveness() {
    local start_time=$(date +%s%3N)

    if docker exec minecraft-server-docker-mc-1 rcon-cli "list" >/dev/null 2>&1; then
        local end_time=$(date +%s%3N)
        local response_time=$((end_time - start_time))
        echo "$response_time"
        return 0
    else
        echo "-1"
        return 1
    fi
}

# Function to get player count
get_player_count() {
    local player_list=$(docker exec minecraft-server-docker-mc-1 rcon-cli "list" 2>/dev/null || echo "")
    local player_count=$(echo "$player_list" | grep -o 'There are [0-9]*' | grep -o '[0-9]*' || echo "0")
    echo "$player_count"
}

# Function to assess performance status
assess_performance() {
    local metric="$1"
    local value="$2"
    local warning_threshold="$3"
    local critical_threshold="$4"
    local reverse_logic="${5:-false}"  # For metrics where lower is worse (like TPS)

    if [[ "$reverse_logic" == "true" ]]; then
        if (( $(echo "$value $critical_threshold" | awk '{print ($1 < $2)}') )); then
            echo "CRITICAL"
        elif (( $(echo "$value $warning_threshold" | awk '{print ($1 < $2)}') )); then
            echo "WARNING"
        else
            echo "OK"
        fi
    else
        if (( $(echo "$value $critical_threshold" | awk '{print ($1 > $2)}') )); then
            echo "CRITICAL"
        elif (( $(echo "$value $warning_threshold" | awk '{print ($1 > $2)}') )); then
            echo "WARNING"
        else
            echo "OK"
        fi
    fi
}

# Function to run comprehensive performance check
run_performance_check() {
    echo "📊 SkyWars Performance Check"
    echo "============================"
    echo "Timestamp: $(date)"
    echo ""

    # Server TPS
    local tps=$(get_server_tps)
    local tps_status=$(assess_performance "TPS" "$tps" "$TPS_WARNING" "$TPS_CRITICAL" "true")
    echo "Server TPS: $tps [$tps_status]"

    # Memory usage
    local memory=$(get_memory_usage)
    local memory_status=$(assess_performance "Memory" "$memory" "$MEMORY_WARNING" "$MEMORY_CRITICAL")
    echo "Memory Usage: ${memory}% [$memory_status]"

    # Disk usage
    local disk=$(get_disk_usage)
    local disk_status=$(assess_performance "Disk" "$disk" "$DISK_WARNING" "$DISK_CRITICAL")
    echo "Disk Usage: ${disk}% [$disk_status]"

    # CPU usage
    local cpu=$(get_cpu_usage)
    echo "CPU Usage: ${cpu}%"

    # Server responsiveness
    local response_time=$(check_server_responsiveness)
    if [[ "$response_time" != "-1" ]]; then
        local response_status=$(assess_performance "Response" "$response_time" "1000" "3000")
        echo "Response Time: ${response_time}ms [$response_status]"
    else
        echo "Response Time: FAILED [CRITICAL]"
    fi

    # Player count
    local players=$(get_player_count)
    echo "Players Online: $players"

    # Overall status
    echo ""
    local overall_status="OK"
    if [[ "$tps_status" == "CRITICAL" || "$memory_status" == "CRITICAL" || "$disk_status" == "CRITICAL" || "$response_time" == "-1" ]]; then
        overall_status="CRITICAL"
    elif [[ "$tps_status" == "WARNING" || "$memory_status" == "WARNING" || "$disk_status" == "WARNING" ]]; then
        overall_status="WARNING"
    fi

    echo "Overall Status: $overall_status"

    # Log to file
    local log_file="logs/performance-$(date +%Y%m%d).log"
    echo "$(date '+%Y-%m-%d %H:%M:%S'),TPS,$tps,$tps_status" >> "$log_file"
    echo "$(date '+%Y-%m-%d %H:%M:%S'),Memory,$memory,$memory_status" >> "$log_file"
    echo "$(date '+%Y-%m-%d %H:%M:%S'),Disk,$disk,$disk_status" >> "$log_file"
    echo "$(date '+%Y-%m-%d %H:%M:%S'),CPU,$cpu,INFO" >> "$log_file"
    echo "$(date '+%Y-%m-%d %H:%M:%S'),Response,$response_time,$response_status" >> "$log_file"
    echo "$(date '+%Y-%m-%d %H:%M:%S'),Players,$players,INFO" >> "$log_file"
    echo "$(date '+%Y-%m-%d %H:%M:%S'),Overall,0,$overall_status" >> "$log_file"

    return $(if [[ "$overall_status" == "OK" ]]; then echo 0; elif [[ "$overall_status" == "WARNING" ]]; then echo 1; else echo 2; fi)
}

# Export functions
export -f get_server_tps
export -f get_memory_usage
export -f get_disk_usage
export -f get_cpu_usage
export -f check_server_responsiveness
export -f get_player_count
export -f assess_performance
export -f run_performance_check
EOF

    chmod +x scripts/system-performance-monitor.sh
    log_success "System performance monitor created"
}

# Create SkyWars specific performance monitor
create_skywars_monitor() {
    log_perf "Creating SkyWars-specific performance monitor..."

    cat > scripts/skywars-performance-monitor.sh << 'EOF'
#!/bin/bash
# SkyWars Performance Monitor
# Monitors SkyWars-specific metrics

# Source database connection manager
source scripts/db-connection-manager.sh 2>/dev/null || true

# Function to get active games count
get_active_games() {
    if command -v execute_sql &> /dev/null; then
        local active_games=$(execute_sql "SELECT COUNT(*) FROM skywars_matches WHERE status = 'ACTIVE';" 2>/dev/null | grep -o '[0-9]*' || echo "0")
        echo "$active_games"
    else
        echo "0"
    fi
}

# Function to get queued players count
get_queued_players() {
    # This would integrate with actual queue system
    # For now, return 0 as placeholder
    echo "0"
}

# Function to get average game duration
get_avg_game_duration() {
    if command -v execute_sql &> /dev/null; then
        local avg_duration=$(execute_sql "SELECT AVG(duration) FROM skywars_matches WHERE status = 'COMPLETED' AND start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR);" 2>/dev/null | grep -o '[0-9]*' || echo "0")
        echo "$avg_duration"
    else
        echo "0"
    fi
}

# Function to get games per hour
get_games_per_hour() {
    if command -v execute_sql &> /dev/null; then
        local games_per_hour=$(execute_sql "SELECT COUNT(*) FROM skywars_matches WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR);" 2>/dev/null | grep -o '[0-9]*' || echo "0")
        echo "$games_per_hour"
    else
        echo "0"
    fi
}

# Function to check plugin performance
check_plugin_performance() {
    local plugins_loaded=0
    local plugins_total=4  # Expected: AuthMe, Essentials, Multiverse, SkyWars

    local plugin_list=$(docker exec minecraft-server-docker-mc-1 rcon-cli "plugins" 2>/dev/null || echo "")

    if echo "$plugin_list" | grep -q "AuthMe"; then ((plugins_loaded++)); fi
    if echo "$plugin_list" | grep -q "Essentials"; then ((plugins_loaded++)); fi
    if echo "$plugin_list" | grep -q "Multiverse"; then ((plugins_loaded++)); fi
    if echo "$plugin_list" | grep -q "SkyWars"; then ((plugins_loaded++)); fi

    local plugin_health=$((plugins_loaded * 100 / plugins_total))
    echo "$plugin_health"
}

# Function to run SkyWars performance check
run_skywars_performance_check() {
    echo "🎮 SkyWars Performance Metrics"
    echo "=============================="
    echo "Timestamp: $(date)"
    echo ""

    # Active games
    local active_games=$(get_active_games)
    echo "Active Games: $active_games"

    # Queued players
    local queued_players=$(get_queued_players)
    echo "Queued Players: $queued_players"

    # Average game duration
    local avg_duration=$(get_avg_game_duration)
    echo "Avg Game Duration: ${avg_duration}s"

    # Games per hour
    local games_per_hour=$(get_games_per_hour)
    echo "Games/Hour: $games_per_hour"

    # Plugin health
    local plugin_health=$(check_plugin_performance)
    echo "Plugin Health: ${plugin_health}%"

    # Database responsiveness
    if command -v test_db_connection &> /dev/null; then
        local db_start=$(date +%s%3N)
        if test_db_connection; then
            local db_end=$(date +%s%3N)
            local db_response=$((db_end - db_start))
            echo "DB Response: ${db_response}ms"
        else
            echo "DB Response: FAILED"
        fi
    else
        echo "DB Response: N/A"
    fi

    # Log SkyWars metrics
    local log_file="logs/skywars-performance-$(date +%Y%m%d).log"
    echo "$(date '+%Y-%m-%d %H:%M:%S'),ActiveGames,$active_games" >> "$log_file"
    echo "$(date '+%Y-%m-%d %H:%M:%S'),QueuedPlayers,$queued_players" >> "$log_file"
    echo "$(date '+%Y-%m-%d %H:%M:%S'),AvgDuration,$avg_duration" >> "$log_file"
    echo "$(date '+%Y-%m-%d %H:%M:%S'),GamesPerHour,$games_per_hour" >> "$log_file"
    echo "$(date '+%Y-%m-%d %H:%M:%S'),PluginHealth,$plugin_health" >> "$log_file"
}

# Export functions
export -f get_active_games
export -f get_queued_players
export -f get_avg_game_duration
export -f get_games_per_hour
export -f check_plugin_performance
export -f run_skywars_performance_check
EOF

    chmod +x scripts/skywars-performance-monitor.sh
    log_success "SkyWars performance monitor created"
}

# Create performance alerting system
create_alerting_system() {
    log_perf "Creating performance alerting system..."

    cat > scripts/performance-alerting.sh << 'EOF'
#!/bin/bash
# Performance Alerting System
# Sends alerts when performance thresholds are exceeded

# Alert configuration
ALERT_EMAIL=""  # Set email for alerts
ALERT_DISCORD_WEBHOOK=""  # Set Discord webhook for alerts
ALERT_LOG_FILE="logs/performance-alerts.log"

# Function to send console alert
send_console_alert() {
    local severity="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    case "$severity" in
        "CRITICAL")
            echo -e "\033[0;31m🚨 CRITICAL ALERT [$timestamp]: $message\033[0m"
            ;;
        "WARNING")
            echo -e "\033[1;33m⚠️  WARNING [$timestamp]: $message\033[0m"
            ;;
        "INFO")
            echo -e "\033[0;34mℹ️  INFO [$timestamp]: $message\033[0m"
            ;;
    esac

    # Log alert
    echo "[$timestamp] $severity: $message" >> "$ALERT_LOG_FILE"
}

# Function to send Discord alert (if webhook configured)
send_discord_alert() {
    local severity="$1"
    local message="$2"

    if [[ -n "$ALERT_DISCORD_WEBHOOK" ]]; then
        local color=""
        local emoji=""

        case "$severity" in
            "CRITICAL")
                color="15158332"  # Red
                emoji="🚨"
                ;;
            "WARNING")
                color="16776960"  # Yellow
                emoji="⚠️"
                ;;
            "INFO")
                color="3447003"   # Blue
                emoji="ℹ️"
                ;;
        esac

        local payload=$(cat << EOF
{
    "embeds": [{
        "title": "$emoji SkyWars Performance Alert",
        "description": "$message",
        "color": $color,
        "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.000Z)",
        "footer": {
            "text": "SkyWars Performance Monitor"
        }
    }]
}
EOF
        )

        curl -H "Content-Type: application/json" -X POST -d "$payload" "$ALERT_DISCORD_WEBHOOK" 2>/dev/null || true
    fi
}

# Function to assess performance status (local copy for alerting)
assess_performance() {
    local metric="$1"
    local value="$2"
    local warning_threshold="$3"
    local critical_threshold="$4"
    local reverse_logic="${5:-false}"  # For metrics where lower is worse (like TPS)

    # Handle empty or invalid values
    if [[ -z "$value" ]] || ! [[ "$value" =~ ^[0-9]+\.?[0-9]*$ ]]; then
        echo "UNKNOWN"
        return
    fi

    if [[ "$reverse_logic" == "true" ]]; then
        # For TPS: lower values are worse
        if (( $(echo "$value < $critical_threshold" | awk '{print ($1 < $2)}') )); then
            echo "CRITICAL"
        elif (( $(echo "$value < $warning_threshold" | awk '{print ($1 < $2)}') )); then
            echo "WARNING"
        else
            echo "OK"
        fi
    else
        # For memory/disk: higher values are worse
        if (( $(echo "$value > $critical_threshold" | awk '{print ($1 > $2)}') )); then
            echo "CRITICAL"
        elif (( $(echo "$value > $warning_threshold" | awk '{print ($1 > $2)}') )); then
            echo "WARNING"
        else
            echo "OK"
        fi
    fi
}

# Function to check and alert on performance metrics
check_and_alert() {
    local metric="$1"
    local value="$2"
    local warning_threshold="$3"
    local critical_threshold="$4"
    local reverse_logic="${5:-false}"

    local status=$(assess_performance "$metric" "$value" "$warning_threshold" "$critical_threshold" "$reverse_logic")

    case "$status" in
        "CRITICAL")
            local message="$metric is at critical level: $value (threshold: $critical_threshold)"
            send_console_alert "CRITICAL" "$message"
            send_discord_alert "CRITICAL" "$message"
            ;;
        "WARNING")
            local message="$metric is at warning level: $value (threshold: $warning_threshold)"
            send_console_alert "WARNING" "$message"
            send_discord_alert "WARNING" "$message"
            ;;
    esac
}

# Function to run performance monitoring with alerts
run_monitoring_with_alerts() {
    # Source performance monitors
    source scripts/system-performance-monitor.sh 2>/dev/null || true

    # Get metrics
    local tps=$(get_server_tps)
    local memory=$(get_memory_usage)
    local disk=$(get_disk_usage)
    local response_time=$(check_server_responsiveness)

    # Check and alert on each metric
    check_and_alert "TPS" "$tps" "18.0" "15.0" "true"
    check_and_alert "Memory" "$memory" "80" "90"
    check_and_alert "Disk" "$disk" "80" "90"

    if [[ "$response_time" != "-1" ]]; then
        check_and_alert "Response Time" "$response_time" "1000" "3000"
    else
        send_console_alert "CRITICAL" "Server is not responding to commands"
        send_discord_alert "CRITICAL" "Server is not responding to commands"
    fi
}

# Export functions
export -f assess_performance
export -f send_console_alert
export -f send_discord_alert
export -f check_and_alert
export -f run_monitoring_with_alerts
EOF

    chmod +x scripts/performance-alerting.sh
    log_success "Performance alerting system created"
}

# Create performance optimization system
create_optimization_system() {
    log_perf "Creating performance optimization system..."

    cat > scripts/performance-optimization.sh << 'EOF'
#!/bin/bash
# Performance Optimization System
# Automatically optimizes system performance

# Function to optimize server performance
optimize_server_performance() {
    echo "🔧 Optimizing server performance..."

    # Restart server if TPS is critically low
    local tps=$(get_server_tps 2>/dev/null || echo "20")
    if (( $(echo "$tps < 15.0" | bc -l) )); then
        echo "Critical TPS detected ($tps), restarting server..."
        docker-compose restart mc

        # Wait for restart
        sleep 30
        echo "Server restarted"
    fi

    # Clear server cache if memory usage is high
    local memory=$(get_memory_usage 2>/dev/null || echo "0")
    if (( $(echo "$memory > 85" | bc -l) )); then
        echo "High memory usage detected (${memory}%), clearing cache..."
        docker exec minecraft-server-docker-mc-1 rcon-cli "gc" 2>/dev/null || true
        echo "Cache cleared"
    fi

    # Clean old log files if disk usage is high
    local disk=$(get_disk_usage 2>/dev/null || echo "0")
    if [[ $disk -gt 85 ]]; then
        echo "High disk usage detected (${disk}%), cleaning old files..."
        find logs/ -name "*.log" -mtime +7 -delete 2>/dev/null || true
        find backups/ -name "*.gz" -mtime +30 -delete 2>/dev/null || true
        echo "Old files cleaned"
    fi
}

# Function to optimize database performance
optimize_database_performance() {
    echo "🗄️ Optimizing database performance..."

    # Source database optimization
    source scripts/db-optimization.sh 2>/dev/null || true

    if command -v optimize_tables &> /dev/null; then
        optimize_tables
    fi

    if command -v cleanup_old_data &> /dev/null; then
        cleanup_old_data 30  # Clean data older than 30 days
    fi
}

# Function to optimize plugin performance
optimize_plugin_performance() {
    echo "🔌 Optimizing plugin performance..."

    # Check plugin health
    local plugin_health=$(check_plugin_performance 2>/dev/null || echo "100")

    if [[ $plugin_health -lt 75 ]]; then
        echo "Plugin health issues detected (${plugin_health}%), reloading plugins..."
        docker exec minecraft-server-docker-mc-1 rcon-cli "reload" 2>/dev/null || true
        sleep 10
        echo "Plugins reloaded"
    fi
}

# Function to run comprehensive optimization
run_comprehensive_optimization() {
    echo "🚀 Running comprehensive performance optimization..."
    echo "=================================================="

    # Run system performance check first
    source scripts/system-performance-monitor.sh 2>/dev/null || true
    local status_code=0
    if command -v run_performance_check &> /dev/null; then
        run_performance_check || status_code=$?
    fi

    # Only optimize if there are performance issues
    if [[ $status_code -gt 0 ]]; then
        echo ""
        echo "Performance issues detected, running optimizations..."

        optimize_server_performance
        optimize_database_performance
        optimize_plugin_performance

        echo ""
        echo "Optimization completed, waiting 30 seconds before re-check..."
        sleep 30

        # Re-check performance
        if command -v run_performance_check &> /dev/null; then
            echo ""
            echo "Post-optimization performance check:"
            run_performance_check
        fi
    else
        echo "System performance is healthy, no optimization needed"
    fi
}

# Export functions
export -f optimize_server_performance
export -f optimize_database_performance
export -f optimize_plugin_performance
export -f run_comprehensive_optimization
EOF

    chmod +x scripts/performance-optimization.sh
    log_success "Performance optimization system created"
}

# Create continuous monitoring daemon
create_monitoring_daemon() {
    log_perf "Creating continuous monitoring daemon..."

    cat > scripts/continuous-performance-monitor.sh << 'EOF'
#!/bin/bash
# Continuous Performance Monitor
# Runs performance checks continuously

# Configuration
CHECK_INTERVAL=300  # 5 minutes
OPTIMIZATION_INTERVAL=1800  # 30 minutes
LOG_RETENTION_DAYS=7

# PID file for daemon management
PID_FILE="logs/performance-monitor.pid"

# Function to start monitoring daemon
start_monitoring_daemon() {
    if [[ -f "$PID_FILE" ]] && kill -0 "$(cat "$PID_FILE")" 2>/dev/null; then
        echo "Performance monitor is already running (PID: $(cat "$PID_FILE"))"
        return 1
    fi

    echo "Starting performance monitoring daemon..."

    # Start daemon in background
    nohup bash -c '
        # Source required scripts
        source scripts/system-performance-monitor.sh 2>/dev/null || true
        source scripts/skywars-performance-monitor.sh 2>/dev/null || true
        source scripts/performance-alerting.sh 2>/dev/null || true
        source scripts/performance-optimization.sh 2>/dev/null || true

        last_optimization=0

        while true; do
            current_time=$(date +%s)

            # Run performance check
            if command -v run_performance_check &> /dev/null; then
                run_performance_check > "logs/monitor-output-$(date +%Y%m%d_%H%M).log" 2>&1
            fi

            # Run SkyWars-specific check
            if command -v run_skywars_performance_check &> /dev/null; then
                run_skywars_performance_check >> "logs/monitor-output-$(date +%Y%m%d_%H%M).log" 2>&1
            fi

            # Run alerting check
            if command -v run_monitoring_with_alerts &> /dev/null; then
                run_monitoring_with_alerts >> "logs/monitor-output-$(date +%Y%m%d_%H%M).log" 2>&1
            fi

            # Run optimization if interval has passed
            if [[ $((current_time - last_optimization)) -gt '$OPTIMIZATION_INTERVAL' ]]; then
                if command -v run_comprehensive_optimization &> /dev/null; then
                    run_comprehensive_optimization >> "logs/optimization-$(date +%Y%m%d_%H%M).log" 2>&1
                fi
                last_optimization=$current_time
            fi

            # Clean old logs
            find logs/ -name "monitor-output-*.log" -mtime +'$LOG_RETENTION_DAYS' -delete 2>/dev/null || true
            find logs/ -name "optimization-*.log" -mtime +'$LOG_RETENTION_DAYS' -delete 2>/dev/null || true

            sleep '$CHECK_INTERVAL'
        done
    ' > logs/performance-daemon.log 2>&1 &

    # Save PID
    echo $! > "$PID_FILE"
    echo "Performance monitoring daemon started (PID: $!)"
}

# Function to stop monitoring daemon
stop_monitoring_daemon() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            rm -f "$PID_FILE"
            echo "Performance monitoring daemon stopped"
        else
            echo "Performance monitoring daemon was not running"
            rm -f "$PID_FILE"
        fi
    else
        echo "Performance monitoring daemon is not running"
    fi
}

# Function to check daemon status
check_daemon_status() {
    if [[ -f "$PID_FILE" ]] && kill -0 "$(cat "$PID_FILE")" 2>/dev/null; then
        echo "Performance monitoring daemon is running (PID: $(cat "$PID_FILE"))"
        return 0
    else
        echo "Performance monitoring daemon is not running"
        return 1
    fi
}

# Main function
main() {
    local action="${1:-status}"

    case "$action" in
        "start")
            start_monitoring_daemon
            ;;
        "stop")
            stop_monitoring_daemon
            ;;
        "restart")
            stop_monitoring_daemon
            sleep 2
            start_monitoring_daemon
            ;;
        "status")
            check_daemon_status
            ;;
        *)
            echo "Usage: $0 {start|stop|restart|status}"
            echo ""
            echo "Commands:"
            echo "  start   - Start performance monitoring daemon"
            echo "  stop    - Stop performance monitoring daemon"
            echo "  restart - Restart performance monitoring daemon"
            echo "  status  - Check daemon status"
            ;;
    esac
}

# Export functions
export -f start_monitoring_daemon
export -f stop_monitoring_daemon
export -f check_daemon_status

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
EOF

    chmod +x scripts/continuous-performance-monitor.sh
    log_success "Continuous monitoring daemon created"
}

# Main execution
main() {
    log_info "Setting up comprehensive performance monitoring..."

    # Create log directory
    mkdir -p logs

    # Create all monitoring components
    create_system_monitor
    create_skywars_monitor
    create_alerting_system
    create_optimization_system
    create_monitoring_daemon

    # Test monitoring systems
    log_info "Testing performance monitoring systems..."
    if ./scripts/system-performance-monitor.sh && source scripts/system-performance-monitor.sh && run_performance_check; then
        log_success "Performance monitoring test passed"
    else
        log_warning "Performance monitoring test had issues"
    fi

    log_success "📊 Performance monitoring setup complete!"
    echo ""
    echo "✅ Monitoring Features:"
    echo "   • System performance monitoring (TPS, Memory, Disk, CPU)"
    echo "   • SkyWars-specific metrics (Games, Players, Database)"
    echo "   • Real-time alerting system"
    echo "   • Automatic performance optimization"
    echo "   • Continuous monitoring daemon"
    echo "   • Performance logging and history"
    echo ""
    echo "🔧 Available Tools:"
    echo "   • ./scripts/system-performance-monitor.sh - System metrics"
    echo "   • ./scripts/skywars-performance-monitor.sh - SkyWars metrics"
    echo "   • ./scripts/performance-alerting.sh - Alert management"
    echo "   • ./scripts/performance-optimization.sh - Auto-optimization"
    echo "   • ./scripts/continuous-performance-monitor.sh - Daemon control"
    echo ""
    echo "📊 Monitoring Operations:"
    echo "   • Real-time performance checks"
    echo "   • Threshold-based alerting"
    echo "   • Automatic optimization triggers"
    echo "   • Historical performance logging"
    echo "   • Continuous background monitoring"
    echo ""
    echo "🚀 Quick Start:"
    echo "   • Start monitoring: ./scripts/continuous-performance-monitor.sh start"
    echo "   • Check status: ./scripts/continuous-performance-monitor.sh status"
    echo "   • View logs: tail -f logs/performance-daemon.log"
    echo ""
    log_success "Performance monitoring is ready! 📊"
}

# Run main function
main "$@"