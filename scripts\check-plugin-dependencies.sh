#!/bin/bash
# Plugin Dependency Checker

echo "🔍 Checking Plugin Dependencies..."
echo "=================================="

# Check server status
if ! docker ps | grep -q minecraft-server-docker-mc-1; then
    echo "❌ Server is not running"
    exit 1
fi

# Get plugin list from server
PLUGINS=$(docker exec minecraft-server-docker-mc-1 rcon-cli "plugins" 2>/dev/null || echo "")

# Check for required plugins
echo "Required Plugins:"
echo "=================="

check_plugin() {
    local plugin_name="$1"
    local required="$2"
    
    if echo "$PLUGINS" | grep -q "$plugin_name"; then
        echo "✅ $plugin_name - Loaded"
        return 0
    else
        if [[ "$required" == "true" ]]; then
            echo "❌ $plugin_name - Missing (Required)"
            return 1
        else
            echo "⚠️  $plugin_name - Missing (Optional)"
            return 0
        fi
    fi
}

ERRORS=0

# Check required plugins
check_plugin "AuthMe" "true" || ((ERRORS++))
check_plugin "Essentials" "true" || ((ERRORS++))
check_plugin "Multiverse-Core" "true" || ((ERRORS++))

echo ""
echo "Optional Plugins:"
echo "================="

# Check optional plugins
check_plugin "Vault" "false"
check_plugin "WorldEdit" "false"
check_plugin "LuckPerms" "false"
check_plugin "PlaceholderAPI" "false"

echo ""
echo "Summary:"
echo "========"

if [[ $ERRORS -eq 0 ]]; then
    echo "✅ All required plugins are loaded"
    exit 0
else
    echo "❌ $ERRORS required plugin(s) missing"
    exit 1
fi
